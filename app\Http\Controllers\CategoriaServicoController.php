<?php

namespace App\Http\Controllers;

use App\Models\CategoriaServico;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CategoriaServicoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $query = CategoriaServico::with(['servicosProdutos'])
            ->daClinica($clinicaId);

        if ($request->has('ativo')) {
            if ($request->ativo == '1') {
                $query->ativas();
            } else {
                $query->where('ativo', false);
            }
        }

        $categorias = $query->orderBy('nome')->get();

        // Adicionar contadores
        $categorias->each(function ($categoria) {
            $categoria->quantidade_servicos = $categoria->getQuantidadeServicos();
            $categoria->quantidade_servicos_ativos = $categoria->getQuantidadeServicosAtivos();
        });

        return responseSuccess(['data' => $categorias]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $validator = Validator::make($request->all(), [
            'nome' => 'required|string|max:255',
            'descricao' => 'nullable|string',
            'cor' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        try {
            $categoria = CategoriaServico::create([
                'clinica_id' => $clinicaId,
                'nome' => $request->nome,
                'descricao' => $request->descricao,
                'cor' => $request->cor ?? '#007bff',
                'ativo' => true,
            ]);

            return responseSuccess([
                'message' => 'Categoria criada com sucesso',
                'data' => $categoria
            ]);

        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao criar categoria: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $categoria = CategoriaServico::with(['servicosProdutos'])
            ->daClinica($clinicaId)
            ->findOrFail($id);

        $categoria->quantidade_servicos = $categoria->getQuantidadeServicos();
        $categoria->quantidade_servicos_ativos = $categoria->getQuantidadeServicosAtivos();

        return responseSuccess(['data' => $categoria]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $categoria = CategoriaServico::daClinica($clinicaId)->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'nome' => 'sometimes|string|max:255',
            'descricao' => 'nullable|string',
            'cor' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        try {
            $categoria->update($request->only(['nome', 'descricao', 'cor']));

            return responseSuccess([
                'message' => 'Categoria atualizada com sucesso',
                'data' => $categoria
            ]);

        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao atualizar categoria: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $categoria = CategoriaServico::daClinica($clinicaId)->findOrFail($id);

        // Verificar se tem serviços/produtos associados
        if ($categoria->getQuantidadeServicos() > 0) {
            return responseError([
                'message' => 'Esta categoria não pode ser excluída pois possui serviços/produtos associados',
                'statusCode' => 422
            ]);
        }

        try {
            $categoria->delete();

            return responseSuccess(['message' => 'Categoria excluída com sucesso']);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao excluir categoria: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Ativar/Desativar categoria
     */
    public function toggleStatus(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $categoria = CategoriaServico::daClinica($clinicaId)->findOrFail($id);

        try {
            if ($categoria->ativo) {
                $categoria->desativar();
                $message = 'Categoria desativada com sucesso';
            } else {
                $categoria->ativar();
                $message = 'Categoria ativada com sucesso';
            }

            return responseSuccess([
                'message' => $message,
                'data' => $categoria->fresh()
            ]);

        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao alterar status: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Obter categorias ativas para seleção
     */
    public function ativas()
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $categorias = CategoriaServico::daClinica($clinicaId)
            ->ativas()
            ->orderBy('nome')
            ->get(['id', 'nome', 'cor']);

        return responseSuccess(['data' => $categorias]);
    }
}
