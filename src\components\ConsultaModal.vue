<template>
  <!-- Modal Editar consulta -->
  <div class="modal fade lumi-fade" tabindex="-1" id="modalEditarConsulta">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{ isNewConsulta ? $t('appointment.title.new') : $t('appointment.title.edit') }}</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
            ref="closeModalEditarConsulta"
          ></button>
        </div>
        <div class="modal-body px-4">
          <div class="row g-3">
            <!-- Primeira linha: Paciente e Ortodontista -->
            <div class="col-7 col-sm-7">
              <label class="form-label">
                <span class="me-1"><font-awesome-icon :icon="['fas', 'user']" /></span>
                {{ $t('appointment.fields.patient') }}:
                <span class="text-danger">*</span>
              </label>
              <div class="position-relative">
                <CustomInputGroup
                  :addonLeft="true"
                  :addonRight="false"
                  :addonLeftClass="!consulta.paciente_id && consulta.paciente_nome ? 'bg-success text-white' : 'bg-light'"
                >
                <!-- Addon à esquerda para mostrar ID da ficha ou "Novo" -->
                <template #addon-left>
                  <span v-if="consulta.paciente_id && selectedPacienteIdFicha" class="addon-content">
                    #{{ formatIdFicha(selectedPacienteIdFicha) }}
                  </span>
                  <span v-else-if="!consulta.paciente_id && consulta.paciente_nome" class="addon-content">
                    {{ $t('appointment.fields.new') }}
                  </span>
                  <span v-else class="addon-content">
                    <font-awesome-icon :icon="['fas', 'search']" />
                  </span>
                </template>

                <!-- Input principal com botão de limpar flutuante -->
                <div class="position-relative">
                  <input
                    type="text"
                    class="form-control input-md paciente-search-input"
                    v-model="pacienteSearch"
                    @focus="showPacienteDropdown = true"
                    @blur="handleInputBlur"
                    @input="filterPacientes"
                    @click="showPacienteDropdown = true"
                    :placeholder="$t('appointment.fields.selectPatient')"
                    :disabled="pacienteFixo"
                    autocomplete="off"
                  />
                  <!-- X flutuante dentro do input -->
                  <button
                    v-if="(consulta.paciente_id || consulta.paciente_nome) && !pacienteFixo"
                    class="floating-clear-button"
                    type="button"
                    @click="clearSelectedPaciente"
                    title="Limpar seleção"
                  >
                    <font-awesome-icon :icon="['fas', 'times']" />
                  </button>
                </div>

                <!-- Sem addon à direita -->
                <template #addon-right>
                </template>
              </CustomInputGroup>

              <!-- Dropdown para seleção de pacientes -->
              <div
                v-show="showPacienteDropdown && !pacienteFixo"
                class="paciente-dropdown"
                :class="{ 'show': showPacienteDropdown }"
              >
                  <div class="paciente-dropdown-content">
                    <div
                      v-for="paciente in filteredPacientes"
                      :key="paciente.id"
                      class="paciente-item"
                      tabindex="0"
                      @click="selectPaciente(paciente)"
                      @keydown.enter="selectPaciente(paciente)"
                    >
                      <div class="d-flex align-items-center">
                        <div class="paciente-id me-2" v-if="paciente.id_ficha">#{{ formatIdFicha(paciente.id_ficha) }}</div>
                        <div class="paciente-nome">{{ paciente.nome }}</div>
                      </div>
                    </div>
                    <div
                      v-if="filteredPacientes.length === 0 && pacienteSearch.trim() !== '' && consulta.paciente_id !== null"
                      class="paciente-item new-paciente"
                      tabindex="0"
                      @click="createNewPaciente"
                      @keydown.enter="createNewPaciente"
                    >
                      <div class="d-flex align-items-center w-100">
                        <div class="paciente-id me-2 bg-success text-white">
                          <font-awesome-icon :icon="['fas', 'plus-circle']" class="me-1" />
                          {{ $t('appointment.fields.new') }}
                        </div>
                        <div class="paciente-nome">
                          {{ $t('appointment.fields.createPatient') }}: {{ pacienteSearch }}
                        </div>
                      </div>
                    </div>
                  </div>
              </div>
              </div>
            </div>

            <div class="col-5 col-sm-5">
              <label class="form-label">
                <span class="me-1"><font-awesome-icon :icon="['fas', 'user-md']" /></span>
                {{ $t('appointment.fields.orthodontist') }}:
                <span class="text-danger">*</span>
              </label>
              <div class="input-group">
                <select
                  class="form-select input-md"
                  v-model="consulta.dentista_id"
                  :disabled="isDentistaSelectDisabled"
                >
                  <option value="" disabled selected>{{ $t('appointment.fields.selectOrthodontist') }}</option>
                  <option
                    v-for="dentista in dentistas"
                    :key="dentista.id"
                    :value="dentista.id"
                  >
                    {{ dentista.nome }}
                  </option>
                </select>
              </div>
            </div>

            <!-- Segunda linha: Data, Horário e Valor -->
            <div class="col-4">
              <div class="d-flex justify-content-between align-items-center">
                <label class="form-label mb-1">
                  <span class="me-1"><font-awesome-icon :icon="['fas', 'calendar']" /></span>
                  {{ $t('appointment.fields.date') }}:
                  <span class="text-danger">*</span>
                </label>
                <small :class="howMuchTimeClass" class="howmuchtime-text">{{ $filters.howMuchTime(consulta.data, { type: 'date' }) }}</small>
              </div>
              <div class="input-group">
                <input
                  type="date"
                  class="form-control input-md date-time-input"
                  v-model="consulta.data"
                >
              </div>
            </div>

            <div class="col-4">
              <label class="form-label">
                <span class="me-1"><font-awesome-icon :icon="['fas', 'clock']" /></span>
                {{ $t('appointment.fields.time') }}:
                <span class="text-danger">*</span>
              </label>
              <div class="input-group">
                <input
                  type="time"
                  class="form-control input-md date-time-input"
                  v-model="consulta.horario"
                >
              </div>

            </div>

            <!-- Valor -->
            <div class="col-4">
              <label class="form-label">
                <span class="me-1"><font-awesome-icon :icon="['fas', 'dollar-sign']" /></span>
                {{ $t('appointment.fields.value') }}:
              </label>
              <CustomInputGroup :addonLeft="true">
                <template #addon-left>
                  <span class="addon-content">R$</span>
                </template>
                <input
                  type="text"
                  class="form-control input-md"
                  v-model="valorFormatado"
                  @input="formatarValor"
                  @focus="onValorFocus"
                  @blur="onValorBlur"
                  placeholder="0,00"
                >
              </CustomInputGroup>
            </div>

            <div class="p-horizontal-divider d-none d-md-block"></div>

            <!-- Categoria - visible only on desktop - primeira linha -->
            <div class="col-12 d-none d-md-block my-0">
              <div class="btn-group w-100 categoria-btn-group categoria-btn-group-top">
                <button
                  v-for="categoria in categorias.slice(0, 3)"
                  :key="categoria.valor"
                  type="button"
                  class="btn categoria-btn-inline"
                  :class="consulta.categoria === categoria.valor ?
                    'btn-' + categoria.cor :
                    'btn-outline-' + categoria.cor"
                  @click="consulta.categoria = categoria.valor"
                >
                  <font-awesome-icon :icon="['fas', categoria.icone]" class="me-2" />
                  {{ categoria.nome }}
                </button>
              </div>
            </div>

            <!-- Categoria - visible only on desktop - segunda linha -->
            <div class="col-12 d-none d-md-block my-0">
              <div class="btn-group w-100 categoria-btn-group categoria-btn-group-bottom">
                <button
                  v-for="categoria in categorias.slice(3)"
                  :key="categoria.valor"
                  type="button"
                  class="btn categoria-btn-inline"
                  :class="consulta.categoria === categoria.valor ?
                    'btn-' + categoria.cor :
                    'btn-outline-' + categoria.cor"
                  @click="consulta.categoria = categoria.valor"
                >
                  <font-awesome-icon :icon="['fas', categoria.icone]" class="me-2" />
                  {{ categoria.nome }}
                </button>
              </div>
            </div>

            <div class="p-horizontal-divider d-none d-md-block"></div>

            <!-- Observações - ocupa toda a largura -->
            <div class="col-12 mt-0">
              <label class="form-label">
                <span class="me-1"><font-awesome-icon :icon="['fas', 'comment-medical']" /></span>
                {{ $t('appointment.fields.notes') }}:
              </label>
              <div class="input-group">
                <textarea
                  class="form-control input-md observacoes-textarea"
                  v-model="consulta.observacoes"
                  rows="3"
                  :placeholder="$t('appointment.fields.notesPlaceholder')"
                ></textarea>
              </div>
            </div>

            <div class="p-horizontal-divider d-none d-md-block"></div>

            <!-- Status - visible only on desktop -->
            <div class="col-12 d-none d-md-block mt-0">
              <div class="btn-group w-100 status-btn-group">
                <button
                  v-if="!isNewConsulta"
                  type="button"
                  class="btn status-btn"
                  :class="consulta.status === 'cancelada' ? 'btn-danger' : 'btn-outline-danger'"
                  @click="consulta.status = 'cancelada'"
                >
                  <div class="status-icon">
                    <font-awesome-icon :icon="['fas', 'ban']" />
                  </div>
                  {{ $t('appointment.status.canceled') }}
                </button>
                <button
                  type="button"
                  class="btn status-btn"
                  :class="consulta.status === 'agendada' ? 'btn-info' : 'btn-outline-info'"
                  @click="consulta.status = 'agendada'"
                >
                  <div class="status-icon">
                    <font-awesome-icon :icon="['fas', 'calendar-day']" />
                  </div>
                  {{ $t('appointment.status.scheduled') }}
                </button>
                <button
                  type="button"
                  class="btn status-btn"
                  :class="consulta.status === 'confirmada' ? 'btn-success' : 'btn-outline-success'"
                  @click="consulta.status = 'confirmada'"
                >
                  <div class="status-icon">
                    <font-awesome-icon :icon="['fas', 'check-circle']" />
                  </div>
                  {{ $t('appointment.status.confirmed') }}
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <!-- Status buttons for mobile - hidden on desktop -->
          <div class="btn-group w-100 d-md-none mb-2 mobile-status-group">
            <button
              v-if="!isNewConsulta"
              type="button"
              class="btn"
              :class="consulta.status === 'cancelada' ? 'btn-danger' : 'btn-outline-danger'"
              @click="consulta.status = 'cancelada'"
            >
              {{ $t('appointment.status.canceled') }}
            </button>
            <button
              type="button"
              class="btn"
              :class="consulta.status === 'agendada' ? 'btn-info' : 'btn-outline-info'"
              @click="consulta.status = 'agendada'"
            >
              {{ $t('appointment.status.scheduled') }}
            </button>
            <button
              type="button"
              class="btn"
              :class="consulta.status === 'confirmada' ? 'btn-success' : 'btn-outline-success'"
              @click="consulta.status = 'confirmada'"
            >
              {{ $t('appointment.status.confirmed') }}
            </button>
          </div>

          <!-- Categoria buttons for mobile - primeira linha -->
          <div class="d-md-none w-100 mb-0">
            <div class="mobile-categoria-container">
              <button
                v-for="categoria in categorias.slice(0, 4)"
                :key="categoria.valor"
                type="button"
                class="btn categoria-mobile-btn"
                :class="consulta.categoria === categoria.valor ?
                  'btn-' + categoria.cor :
                  'btn-outline-' + categoria.cor"
                @click="consulta.categoria = categoria.valor"
              >
                <font-awesome-icon :icon="['fas', categoria.icone]" class="me-1" />
                <span>{{ categoria.nome }}</span>
              </button>
            </div>
          </div>

          <!-- Categoria buttons for mobile - segunda linha -->
          <div class="d-md-none w-100 mb-2">
            <div class="mobile-categoria-container">
              <button
                v-for="categoria in categorias.slice(4)"
                :key="categoria.valor"
                type="button"
                class="btn categoria-mobile-btn"
                :class="consulta.categoria === categoria.valor ?
                  'btn-' + categoria.cor :
                  'btn-outline-' + categoria.cor"
                @click="consulta.categoria = categoria.valor"
              >
                <font-awesome-icon :icon="['fas', categoria.icone]" class="me-1" />
                <span>{{ categoria.nome }}</span>
              </button>
            </div>
          </div>

          <div class="w-100 d-flex justify-content-end">
            <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">
              {{ $t('appointment.buttons.cancel') }}
            </button>
            <button
              type="button"
              class="btn btn-primary"
              @click="salvarConsulta"
              :disabled="!isFormValid || isLoading"
            >
              <span v-if="isLoading" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              {{ $t('appointment.buttons.save') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import { searchPacientes, getListaSimplesPacientes } from "@/services/pacientesService";
import { getDentistas } from "@/services/dentistasService";
import { novaConsulta, atualizarConsulta, getConsulta } from "@/services/consultasService";
import cSwal from "@/utils/cSwal.js";
import { openModal, closeModalWithAnimation } from "@/utils/modalHelper.js";
import CustomInputGroup from "@/components/CustomInputGroup.vue";

export default {
  name: "ConsultaModal",
  components: {
    CustomInputGroup
  },
  props: {
    pacienteId: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      isLoading: false,
      pacientes: [],
      pacientesLista: [], // Lista simples de pacientes (id, nome, id_ficha)
      pacienteSearch: '', // Texto de busca para o paciente
      showPacienteDropdown: false, // Controla a exibição do dropdown
      filteredPacientes: [], // Pacientes filtrados pela busca
      isSelectingPaciente: false, // Flag para controlar se está selecionando um paciente
      dentistas: [],
      consulta: this.getEmptyConsulta(),
      isNewConsulta: true,
      pacienteFixo: false,
      valorFormatado: '', // Valor formatado para exibição
      categorias: [
        { valor: 'acompanhamento', nome: 'Acompanhamento/ativação', cor: 'info', icone: 'wrench' },
        { valor: 'primeira_consulta', nome: 'Primeira consulta', cor: 'success', icone: 'clipboard-check' },
        { valor: 'emergencia', nome: 'Emergência', cor: 'danger', icone: 'exclamation-triangle' },
        { valor: 'montagem', nome: 'Montagem', cor: 'secondary', icone: 'tools' },
        { valor: 'remocao', nome: 'Remoção', cor: 'secondary', icone: 'minus-circle' },
        { valor: 'replanejamento', nome: 'Replanejamento', cor: 'dark', icone: 'sync-alt' },
        { valor: 'pos_tratamento', nome: 'Pós-tratamento', cor: 'secondary', icone: 'check-double' }
      ]
    };
  },
  computed: {
    isFormValid() {
      // Verificar se tem um paciente selecionado OU um nome de paciente para criar
      const hasPatient = this.consulta.paciente_id ||
                        (this.consulta.paciente_nome && this.consulta.paciente_nome.trim() !== '');

      return (
        hasPatient &&
        this.consulta.dentista_id &&
        this.consulta.data &&
        this.consulta.horario
      );
    },
    // Retorna o ID da ficha do paciente selecionado
    selectedPacienteIdFicha() {
      if (!this.consulta.paciente_id) return null;

      const paciente = this.pacientesLista.find(p => p.id == this.consulta.paciente_id);
      return paciente ? paciente.id_ficha : null;
    },
    howMuchTimeClass() {
      if (!this.consulta.data
        || new Date(this.consulta.data).getFullYear() < 1000) return 'howmuchtime-text';

      // Usar moment para trabalhar com datas de forma mais segura com fuso horário
      const now = moment();
      const today = moment().startOf('day');
      const consultaDate = moment(this.consulta.data);

      // Verificar se a data da consulta é hoje (comparando apenas as datas)
      if (consultaDate.format('YYYY-MM-DD') === today.format('YYYY-MM-DD')) {
        // Se for hoje, verificar se o horário já passou
        if (this.consulta.horario) {
          // Criar um momento com a data e hora da consulta
          const consultaDateTime = moment(this.consulta.data + ' ' + this.consulta.horario);

          // Verificar se o horário da consulta já passou
          if (consultaDateTime.isBefore(now)) {
            return 'howmuchtime-text howmuchtime-past';
          } else {
            // Horário de hoje que ainda não passou
            return 'howmuchtime-text howmuchtime-today';
          }
        }
        // Se não temos horário, mostrar como hoje (verde)
        return 'howmuchtime-text howmuchtime-today';
      } else if (consultaDate.isAfter(today)) {
        // Data futura (amanhã ou depois)
        return 'howmuchtime-text howmuchtime-future';
      } else {
        // Data passada (ontem ou antes)
        return 'howmuchtime-text howmuchtime-past';
      }
    },
    // Verifica se o select do dentista deve estar desabilitado
    isDentistaSelectDisabled() {
      // Verificar se o store e o user estão disponíveis
      if (!this.$store || !this.$store.state || !this.$store.state.token) {
        return true; // Desabilitar por segurança se não tiver acesso ao store
      }

      // Se $user não existir ou não for system_admin, desabilitar o select
      return !this.$user || !this.$user.system_admin;
    }
  },
  watch: {
    // Observar mudanças no $user para atualizar o dentista quando necessário
    '$user': {
      handler(newUser) {
        // Verificar se o store está disponível
        if (!this.$store || !this.$store.state) {
          return;
        }

        if (newUser && !newUser.system_admin && this.$dentista && this.$dentista.id) {
          // Se o usuário não for admin e temos um dentista logado, atualizar o formulário
          if (this.consulta && !this.consulta.dentista_id) {
            this.consulta.dentista_id = this.$dentista.id;
          }
        }
      },
      immediate: true
    },
    // Observar mudanças no valor da consulta para atualizar o valor formatado
    'consulta.valor': {
      handler() {
        this.atualizarValorFormatado();
      }
    }
  },
  async created() {
    await this.carregarDentistas();
    await this.carregarPacientes();
    await this.carregarListaSimplesPacientes();

    // Se tiver um pacienteId definido, fixar o paciente
    if (this.pacienteId) {
      this.consulta.paciente_id = this.pacienteId;
      this.pacienteFixo = true;
      this.onPacienteChange();
    }

    // Garantir que o dentista seja preenchido se necessário após o carregamento
    this.$nextTick(() => {
      if (this.$store && this.$store.state && (!this.$user || !this.$user.system_admin) && this.$dentista && this.$dentista.id) {
        if (!this.consulta.dentista_id) {
          this.consulta.dentista_id = this.$dentista.id;
        }
      }
    });
  },
  mounted() {
    // Configurar animação de fechamento do modal
    const modalEditarConsulta = document.getElementById('modalEditarConsulta');
    if (modalEditarConsulta) {
      // Função para fechar o dropdown quando o modal começar a fechar
      const handleModalHide = () => {
        modalEditarConsulta.classList.add('modal-closing');
        // Fechar o dropdown quando o modal começar a fechar
        this.showPacienteDropdown = false;
      };

      // Função para quando o modal estiver completamente fechado
      const handleModalHidden = () => {
        // Remover a classe após o modal estar completamente fechado
        modalEditarConsulta.classList.remove('modal-closing');
        // Garantir que o dropdown esteja fechado quando o modal estiver completamente fechado
        this.showPacienteDropdown = false;
      };

      // Adicionar os event listeners
      modalEditarConsulta.addEventListener('hide.bs.modal', handleModalHide);
      modalEditarConsulta.addEventListener('hidden.bs.modal', handleModalHidden);

      // Adicionar evento para fechar o dropdown quando clicar no backdrop do modal
      document.addEventListener('click', (event) => {
        const modalBackdrop = document.querySelector('.modal-backdrop');
        if (modalBackdrop && modalBackdrop.contains(event.target)) {
          this.showPacienteDropdown = false;
        }
      });

      // Adicionar evento de clique fora do dropdown para fechá-lo
      // Usar nextTick para garantir que o DOM esteja completamente carregado
      this.$nextTick(() => {
        document.addEventListener('click', this.handleClickOutside);

        // Adicionar evento para fechar o dropdown quando clicar em qualquer lugar do modal
        // exceto no input de busca e no dropdown
        modalEditarConsulta.addEventListener('click', (event) => {
          const dropdown = modalEditarConsulta.querySelector('.paciente-dropdown');
          const input = modalEditarConsulta.querySelector('.paciente-search-input');

          if (dropdown && input &&
              !dropdown.contains(event.target) &&
              !input.contains(event.target) &&
              this.showPacienteDropdown) {
            this.showPacienteDropdown = false;
          }
        });
      });
    }
  },
  beforeUnmount() {
    // Remover os event listeners quando o componente for desmontado
    document.removeEventListener('click', this.handleClickOutside);

    // Remover o event listener do modal
    const modalEditarConsulta = document.getElementById('modalEditarConsulta');
    if (modalEditarConsulta) {
      // Remover os event listeners do modal
      // Nota: não podemos remover listeners anônimos, então estamos apenas limpando
      // os que podemos remover diretamente

      // Remover todos os event listeners de click do modal
      const newModal = modalEditarConsulta.cloneNode(true);
      if (modalEditarConsulta.parentNode) {
        modalEditarConsulta.parentNode.replaceChild(newModal, modalEditarConsulta);
      }
    }

    // Garantir que o dropdown esteja fechado
    this.showPacienteDropdown = false;
  },
  methods: {
    getEmptyConsulta() {
      return {
        id: null,
        paciente_id: this.pacienteId || null, // Usar null como padrão para indicar "Novo paciente"
        paciente_nome: "",
        dentista_id: this.getDentistaIdPadrao(),
        data: moment().format('YYYY-MM-DD'),
        horario: moment().format('HH:mm'),
        valor: "",
        observacoes: "",
        status: "agendada",
        categoria: "acompanhamento" // Valor padrão
      };
    },
    getDentistaIdPadrao() {
      // Verificar se o store está disponível
      if (!this.$store || !this.$store.state) {
        return "";
      }

      // Verificar se $user existe antes de acessar suas propriedades
      if (!this.$user || !this.$user.system_admin) {
        // Se não for system_admin ou $user não existir, tentar usar o dentista logado
        if (this.$dentista && this.$dentista.id) {
          return this.$dentista.id;
        }
      }
      // Se for system_admin ou não tiver dentista logado, retornar string vazia
      return "";
    },
    getDentistaIdParaEdicao(dentistaIdOriginal) {
      // Verificar se o store está disponível
      if (!this.$store || !this.$store.state) {
        return dentistaIdOriginal || "";
      }

      // Verificar se $user existe antes de acessar suas propriedades
      if (!this.$user || !this.$user.system_admin) {
        // Se não for system_admin ou $user não existir, sempre usar o dentista logado
        if (this.$dentista && this.$dentista.id) {
          return this.$dentista.id;
        }
      }
      // Se for system_admin, manter o dentista original
      return dentistaIdOriginal || "";
    },
    async carregarDentistas() {
      try {
        const response = await getDentistas();
        if (response) {
          this.dentistas = response;
        }
      } catch (error) {
        console.error("Erro ao carregar dentistas:", error);
        cSwal.cError(this.$t('appointment.alerts.errorLoadingDentists'));
      }
    },
    async carregarPacientes() {
      try {
        const response = await searchPacientes();
        if (response) {
          this.pacientes = response;
        }
      } catch (error) {
        console.error("Erro ao carregar pacientes:", error);
        cSwal.cError(this.$t('appointment.alerts.errorLoadingPatients'));
      }
    },
    async carregarListaSimplesPacientes() {
      try {
        const response = await getListaSimplesPacientes();
        if (response) {
          this.pacientesLista = response;
          this.filteredPacientes = [...this.pacientesLista];
        }
      } catch (error) {
        console.error("Erro ao carregar lista simples de pacientes:", error);
        cSwal.cError(this.$t('appointment.alerts.errorLoadingPatients'));
      }
    },
    onPacienteChange() {
      // Atualizar o nome do paciente quando o ID for selecionado
      if (this.consulta.paciente_id) {
        const pacienteSelecionado = this.pacientes.find(p => p.id == this.consulta.paciente_id);
        if (pacienteSelecionado) {
          this.consulta.paciente_nome = pacienteSelecionado.nome;
          this.pacienteSearch = pacienteSelecionado.nome;
        }
      }
    },
    // Formata o ID da ficha para ter no mínimo 3 dígitos
    formatIdFicha(id) {
      if (!id) return '';
      return id.toString().padStart(3, '0');
    },
    // Filtra os pacientes com base no texto digitado
    filterPacientes() {
      // Garantir que o dropdown seja exibido quando o usuário digita
      this.showPacienteDropdown = true;

      // Formatar o nome do paciente com primeira letra maiúscula
      this.pacienteSearch = this.formatarNomePaciente(this.pacienteSearch);

      // Se o texto de busca não corresponder exatamente ao nome do paciente selecionado,
      // considerar como um novo paciente
      if (this.consulta.paciente_id &&
          this.consulta.paciente_nome &&
          this.pacienteSearch !== this.consulta.paciente_nome) {
        this.consulta.paciente_id = null;
        this.consulta.paciente_nome = this.pacienteSearch.trim();
      }

      // Se o paciente já está definido como novo, atualizar o nome conforme digita
      if (this.consulta.paciente_id === null && this.pacienteSearch.trim() !== '') {
        this.consulta.paciente_nome = this.pacienteSearch.trim();
      }

      if (!this.pacienteSearch.trim()) {
        this.filteredPacientes = [...this.pacientesLista];
        return;
      }

      this.filtrarListaPacientes();
    },

    // Função auxiliar para filtrar a lista de pacientes
    filtrarListaPacientes() {
      const searchLower = this.pacienteSearch.toLowerCase();

      this.filteredPacientes = this.pacientesLista.filter(paciente => {
        // Busca pelo nome
        const nameMatch = paciente.nome.toLowerCase().includes(searchLower);

        // Busca pelo ID da ficha (se existir)
        const idFichaMatch = paciente.id_ficha &&
          (paciente.id_ficha.toString().includes(searchLower) ||
           `#${this.formatIdFicha(paciente.id_ficha)}`.includes(searchLower));

        return nameMatch || idFichaMatch;
      });
    },
    // Seleciona um paciente do dropdown
    selectPaciente(paciente) {
      try {
        // Marcar que está selecionando um paciente para evitar que o blur feche o dropdown
        this.isSelectingPaciente = true;

        // Prevenir que o método seja chamado com dados inválidos
        if (!paciente || !paciente.id || !paciente.nome) {
          console.warn('Tentativa de selecionar paciente com dados inválidos:', paciente);
          this.isSelectingPaciente = false;
          // Garantir que o dropdown seja fechado mesmo em caso de erro
          this.showPacienteDropdown = false;
          return;
        }

        this.consulta.paciente_id = paciente.id;
        this.consulta.paciente_nome = this.formatarNomePaciente(paciente.nome);
        this.pacienteSearch = this.consulta.paciente_nome;

        // Fechar o dropdown imediatamente
        this.showPacienteDropdown = false;

        // Resetar a flag após um pequeno delay
        setTimeout(() => {
          this.isSelectingPaciente = false;
        }, 100);
      } catch (error) {
        console.error('Erro ao selecionar paciente:', error);
        this.isSelectingPaciente = false;
        // Garantir que o dropdown seja fechado mesmo em caso de erro
        this.showPacienteDropdown = false;
      }
    },
    // Cria um novo paciente com o nome digitado
    createNewPaciente() {
      try {
        // Marcar que está selecionando um paciente para evitar que o blur feche o dropdown
        this.isSelectingPaciente = true;

        if (!this.pacienteSearch.trim()) {
          this.isSelectingPaciente = false;
          // Garantir que o dropdown seja fechado
          this.showPacienteDropdown = false;
          return;
        }

        // Verificar se já está definido como novo com o mesmo nome
        if (this.consulta.paciente_id === null &&
            this.consulta.paciente_nome === this.pacienteSearch.trim()) {
          this.showPacienteDropdown = false;
          this.isSelectingPaciente = false;
          return;
        }

        this.consulta.paciente_id = null;
        this.consulta.paciente_nome = this.formatarNomePaciente(this.pacienteSearch.trim());

        // Fechar o dropdown imediatamente
        this.showPacienteDropdown = false;

        // Resetar a flag após um pequeno delay
        setTimeout(() => {
          this.isSelectingPaciente = false;
        }, 100);
      } catch (error) {
        console.error('Erro ao criar novo paciente:', error);
        this.isSelectingPaciente = false;
        // Garantir que o dropdown seja fechado mesmo em caso de erro
        this.showPacienteDropdown = false;
      }
    },
    // Limpa o paciente selecionado
    clearSelectedPaciente() {
      this.consulta.paciente_id = '';
      this.consulta.paciente_nome = '';
      this.pacienteSearch = '';
      this.showPacienteDropdown = true; // Mostra o dropdown para facilitar nova seleção

      // Foca no input após limpar - usando try/catch para evitar erros
      try {
        this.$nextTick(() => {
          // Usar document.querySelector com um seletor mais específico
          const modalId = 'modalEditarConsulta';
          const modal = document.getElementById(modalId);

          if (modal) {
            const input = modal.querySelector('.paciente-search-input');
            if (input) {
              input.focus();

              // Adicionar um event listener temporário para garantir que o dropdown apareça quando o usuário digitar
              const handleInput = () => {
                this.showPacienteDropdown = true;
                // Remover o event listener após a primeira digitação
                input.removeEventListener('input', handleInput);
              };

              // Adicionar o event listener
              input.addEventListener('input', handleInput);
            }
          }
        });
      } catch (error) {
        console.error('Erro ao focar no input:', error);
      }
    },
    // Fecha o dropdown quando clica fora dele
    handleClickOutside(event) {
      // Verificar se o componente está montado e se this.$el é um elemento DOM válido
      if (!this.$el || !this.$el.nodeType || typeof this.$el.querySelector !== 'function') {
        return;
      }

      try {
        // Usar document.querySelector com um seletor mais específico para garantir que estamos
        // obtendo elementos dentro deste componente específico
        const modalId = 'modalEditarConsulta';
        const modal = document.getElementById(modalId);

        if (!modal) return;

        const dropdown = modal.querySelector('.paciente-dropdown');
        const input = modal.querySelector('.paciente-search-input');

        // Se o clique não foi no dropdown nem no input, fechar o dropdown
        if (dropdown && input && !dropdown.contains(event.target) && !input.contains(event.target)) {
          this.showPacienteDropdown = false;
        }
      } catch (error) {
        console.error('Erro ao verificar clique fora do dropdown:', error);
        // Em caso de erro, garantir que o dropdown seja fechado
        this.showPacienteDropdown = false;
      }
    },

    // Fecha o dropdown quando o foco sai do input
    handleInputBlur(event) {
      // Se estiver no processo de selecionar um paciente, não fechar o dropdown
      if (this.isSelectingPaciente) {
        return;
      }

      try {
        // Verificar se o clique foi dentro do dropdown
        const relatedTarget = event.relatedTarget;

        // Usar document.querySelector com um seletor mais específico
        const modalId = 'modalEditarConsulta';
        const modal = document.getElementById(modalId);

        if (!modal) {
          this.showPacienteDropdown = false;
          return;
        }

        const dropdown = modal.querySelector('.paciente-dropdown');

        // Se o foco não foi para um elemento dentro do dropdown, fechar o dropdown
        if (!dropdown || (relatedTarget && !dropdown.contains(relatedTarget))) {
          // Fechar o dropdown imediatamente
          this.showPacienteDropdown = false;
        }
      } catch (error) {
        console.error('Erro ao verificar blur do input:', error);
        // Em caso de erro, garantir que o dropdown seja fechado
        this.showPacienteDropdown = false;
      }
    },
    // Formatar nome do paciente com primeira letra maiúscula
    formatarNomePaciente(nome) {
      if (!nome) return '';

      return nome
        .toLowerCase()
        .split(' ')
        .map(palavra => {
          if (palavra.length === 0) return palavra;
          return palavra.charAt(0).toUpperCase() + palavra.slice(1);
        })
        .join(' ');
    },
    // Formatar valor monetário
    formatarValor(event) {
      let valor = event.target.value;

      // Remove tudo que não é número
      valor = valor.replace(/\D/g, '');

      // Se estiver vazio, mostrar 0,00
      if (valor === '') {
        this.valorFormatado = '0,00';
        this.consulta.valor = '';
        return;
      }

      // Converte para número e divide por 100 para ter os centavos
      const numeroValor = parseInt(valor) / 100;

      // Formatar com separadores brasileiros
      this.valorFormatado = numeroValor.toLocaleString('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // Salvar o valor numérico para envio
      this.consulta.valor = numeroValor;
    },
    // Quando o campo de valor recebe foco
    onValorFocus() {
      // Se o valor for 0,00, limpar o campo
      if (this.valorFormatado === '0,00') {
        this.valorFormatado = '';
      }
    },
    // Quando o campo de valor perde o foco
    onValorBlur() {
      // Se estiver vazio, mostrar 0,00
      if (this.valorFormatado === '' || this.valorFormatado === '0') {
        this.valorFormatado = '0,00';
        this.consulta.valor = '';
      }
    },
    // Atualizar valorFormatado quando consulta.valor mudar
    atualizarValorFormatado() {
      if (this.consulta.valor === '' || this.consulta.valor === null || this.consulta.valor === undefined) {
        this.valorFormatado = '0,00';
      } else {
        const valor = parseFloat(this.consulta.valor);
        if (isNaN(valor)) {
          this.valorFormatado = '0,00';
        } else {
          this.valorFormatado = valor.toLocaleString('pt-BR', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
          });
        }
      }
    },
    resetForm() {
      this.consulta = this.getEmptyConsulta();
      this.isNewConsulta = true;
      this.pacienteSearch = '';
      this.showPacienteDropdown = false;
      this.valorFormatado = '0,00'; // Resetar valor formatado

      // Se não tiver um paciente fixo, definir o estado para "Novo paciente"
      if (!this.pacienteFixo) {
        this.consulta.paciente_id = null;
      }

      // Garantir que o dentista seja preenchido corretamente para não system_admin
      if (this.$store && this.$store.state && (!this.$user || !this.$user.system_admin) && this.$dentista && this.$dentista.id) {
        this.consulta.dentista_id = this.$dentista.id;
      }
    },
    async abrirModalNovaConsulta() {
      this.resetForm();
      this.isNewConsulta = true;
      openModal('modalEditarConsulta');

      // Garantir que o dropdown esteja fechado quando o modal abrir
      this.showPacienteDropdown = false;
    },
    async abrirModalNovaConsultaComHorario(data, horario = null) {
      this.resetForm();
      this.isNewConsulta = true;

      // Pré-preencher a data
      if (data) {
        const dataObj = new Date(data);
        this.consulta.data = dataObj.toISOString().split('T')[0]; // Formato YYYY-MM-DD

        // Se horário foi fornecido, pré-preencher também
        if (horario) {
          this.consulta.horario = horario; // Formato HH:MM
        }
      }

      openModal('modalEditarConsulta');

      // Garantir que o dropdown esteja fechado quando o modal abrir
      this.showPacienteDropdown = false;
    },
    async abrirModalEditarConsulta(consultaId) {
      try {
        this.isLoading = true;
        this.isNewConsulta = false;

        // Buscar os dados da consulta
        const consulta = await getConsulta(consultaId);

        if (!consulta) {
          cSwal.cError(this.$t('appointment.alerts.errorLoadingAppointment'));
          return;
        }

        // Extrair a data e o horário do timestamp
        let dataHora;
        if (consulta.horario) {
          dataHora = moment(consulta.horario);
        } else if (consulta.date) {
          dataHora = moment(consulta.date);
        } else if (consulta.data) {
          dataHora = moment(consulta.data);
        } else {
          dataHora = moment();
        }

        // Preencher o formulário de edição
        this.consulta = {
          id: consulta.id || consultaId,
          paciente_id: consulta.paciente_id || this.pacienteId || '',
          paciente_nome: consulta.paciente_nome || '',
          dentista_id: this.getDentistaIdParaEdicao(consulta.dentista_id),
          data: dataHora.format('YYYY-MM-DD'),
          horario: dataHora.format('HH:mm'),
          valor: consulta.valor || '',
          observacoes: consulta.observacoes || '',
          status: consulta.status || 'agendada',
          categoria: consulta.categoria || 'acompanhamento'
        };

        // Atualizar o campo de busca de paciente
        this.pacienteSearch = this.consulta.paciente_nome;

        // Atualizar o valor formatado
        this.atualizarValorFormatado();

        // Se tiver um pacienteId definido, fixar o paciente
        if (this.pacienteId) {
          this.pacienteFixo = true;
        }

        // Abrir o modal
        openModal('modalEditarConsulta');

        // Garantir que o dropdown esteja fechado quando o modal abrir
        this.showPacienteDropdown = false;
      } catch (error) {
        console.error("Erro ao carregar consulta para edição:", error);
        cSwal.cError(this.$t('appointment.alerts.errorLoadingAppointment'));
      } finally {
        this.isLoading = false;
      }
    },
    async salvarConsulta() {
      if (!this.isFormValid) {
        cSwal.cAlert(this.$t('appointment.alerts.requiredFields'));
        return;
      }

      this.isLoading = true;

      try {
        // Criar uma cópia do objeto para não modificar o original
        const consultaData = {
          paciente_id: this.consulta.paciente_id,
          paciente_nome: this.consulta.paciente_nome,
          dentista_id: this.consulta.dentista_id,
          horario: this.consulta.horario,
          valor: this.consulta.valor ? parseFloat(this.consulta.valor) : null,
          observacoes: this.consulta.observacoes,
          status: this.consulta.status,
          categoria: this.consulta.categoria,
          data: this.consulta.data, // Será usado pelo serviço para combinar com o horário
          criar_paciente: this.consulta.paciente_id === null && this.consulta.paciente_nome ? true : false
        };

        let response;

        if (this.isNewConsulta) {
          response = await novaConsulta(consultaData);
          if (response) {
            cSwal.cSuccess(this.$t('appointment.alerts.successSaving'));

            // Se criou um novo paciente, atualizar as listas
            if (consultaData.criar_paciente) {
              await this.carregarPacientes();
              await this.carregarListaSimplesPacientes();
            }
          } else {
            cSwal.cError(this.$t('appointment.alerts.errorSaving'));
          }
        } else {
          response = await atualizarConsulta(this.consulta.id, consultaData);
          if (response) {
            cSwal.cSuccess(this.$t('appointment.alerts.successSaving'));
          } else {
            cSwal.cError(this.$t('appointment.alerts.errorSaving'));
          }
        }

        if (response) {
          // Fechar o modal com animação
          closeModalWithAnimation('modalEditarConsulta');
          this.resetForm();

          // Emitir evento para atualizar a lista de consultas
          this.$emit('consulta-salva');
        }
      } catch (error) {
        console.error("Erro ao salvar consulta:", error);
        cSwal.cError(this.$t('appointment.alerts.errorSaving'));
      } finally {
        this.isLoading = false;
      }
    }
  }
};
</script>

<style scoped>
/* Modal styling */
.modal-content {
  border-radius: 0.75rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: none;
}

.modal-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
}

.modal-header .modal-title {
  font-weight: 600;
  font-size: 1.25rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
}

/* Form styling */
.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.status-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 0.5rem;
  font-size: 0.85rem;
  font-weight: 500;
}

.categoria-btn-inline {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.35rem;
  font-size: 0.8rem;
  font-weight: 500;
  height: 38px;
  margin-bottom: 0px;
}

.categoria-btn-group, .status-btn-group {
  /* border-radius: 0.375rem; */
  overflow: hidden;
}

.categoria-btn-group-top > .btn:first-child {
  border-bottom-left-radius: 0;
}

.categoria-btn-group-top > .btn:last-child {
  border-bottom-right-radius: 0;
}

.categoria-btn-group-bottom > .btn:first-child {
  border-top-left-radius: 0;
}

.categoria-btn-group-bottom > .btn:last-child {
  border-top-right-radius: 0;
}

.categoria-btn-group button {
  flex: 1;
}

.btn-group-divider {
  width: 100%;
  height: 1px;
  background: #DDD;
  border-right: 1px solid #000;
  border-left: 1px solid #000;
}

.howmuchtime-text {
  font-size: 0.85rem;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
}

.howmuchtime-today {
  color: #198754;
  background-color: rgba(25, 135, 84, 0.1);
}

.howmuchtime-past {
  color: #fd7e14;
  background-color: rgba(253, 126, 20, 0.1);
}

.howmuchtime-future {
  color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.1);
}

.status-icon {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.relative-time-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(var(--lumi-input-height) + 5px);
  padding: 6px 12px;
  border-radius: 6px;
  background-color: #1470e9;
  color: #EEE;
  font-weight: 500;
  font-size: 0.95rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  width: 100%;
}

.mobile-status-group .btn {
  font-size: 0.75rem;
  padding: 0.5rem 0.25rem;
}

.mobile-categoria-group .btn {
  font-size: 0.7rem;
  padding: 0.4rem 0.2rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}



.observacoes-textarea {
  resize: vertical;
  min-height: 80px;
  font-size: 0.9rem !important;
  line-height: 1.4;
  border-color: #ced4da;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.observacoes-textarea:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.date-time-input {
  font-family: inherit;
  font-size: 1rem !important;
  font-weight: 500;
}

/* Input MD customization */
.input-md {
  height: calc(var(--lumi-input-height) + 5px) !important;
  font-size: 0.95rem !important;
}

/* Disabled select styling */
.form-select:disabled {
  background-color: #f8f9fa;
  opacity: 0.8;
  cursor: not-allowed;
}

/* Status button group styling */
.btn-group .btn {
  font-size: 0.85rem;
  padding: 0.375rem 0.5rem;
  transition: transform 0.2s ease, opacity 0.2s ease;
  border-width: 1px;
}

.btn-group .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Animação de fade para o modal */
.lumi-fade.modal-closing .modal-dialog {
  transform: translate(0, -25px);
  transition: transform 0.3s ease-out;
}

.lumi-fade.modal-closing {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

/* Paciente dropdown styles */
.paciente-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-top: 2px; /* Ajustado para ficar colado ao input */
  background-color: #fff;
  width: 100%;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.paciente-dropdown.show {
  max-height: 300px;
  overflow-y: auto;
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.paciente-dropdown-content {
  padding: 0.5rem 0;
}

.paciente-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.paciente-item:hover,
.paciente-item:focus {
  background-color: rgba(0, 123, 255, 0.1);
  outline: none;
}

.paciente-nome {
  font-weight: 500;
  font-size: 0.9rem;
}

.paciente-id {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 600;
  background-color: #f8f9fa;
  padding: 0.15rem 0.5rem;
  border-radius: 0.25rem;
  min-width: 60px;
  text-align: center;
}

/* Estilo para garantir que o conteúdo do addon tenha a mesma largura */
.addon-content {
  display: inline-block;
  min-width: 40px;
  text-align: center;
  font-weight: 500;
}

/* Estilo para o addon do input
.input-group-text {
  font-weight: 500;
  min-width: 70px;
  justify-content: center;
  transition: all 0.2s ease;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}*/

/* Estilo para o botão de limpar flutuante */
.floating-clear-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: all 0.2s ease;
  font-size: 0.85rem;
  opacity: 0.7;
  z-index: 5;
  border-radius: 50%;
}

.floating-clear-button:hover {
  color: #dc3545;
  opacity: 1;
  background-color: rgba(220, 53, 69, 0.1);
}

.new-paciente {
  border-top: 1px dashed #dee2e6;
  margin-top: 0.5rem;
  padding-top: 0.75rem;
  color: #0d6efd;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
  .modal-dialog {
    margin: 0.5rem auto;
    max-width: 92%;
    height: auto;
  }

  .modal-dialog-scrollable .modal-content {
    max-height: 85vh;
  }

  .modal-header {
    padding: 0.75rem 1rem;
  }

  .modal-body {
    padding: 0.75rem;
    overflow-y: auto;
    max-height: calc(85vh - 130px); /* Subtract header and footer height */
  }

  .modal-footer {
    padding: 0.75rem 1rem;
  }

  .btn-group .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.35rem;
    height: auto;
  }

  .status-icon, .categoria-icon {
    display: none;
  }

  .input-md {
    height: calc(var(--lumi-input-height) + 2px) !important;
    font-size: 0.9rem !important;
  }

  .mobile-categoria-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    width: 100%;
  }

  .categoria-mobile-btn {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 0.75rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    text-align: left;
    height: 2.5rem;
    overflow: hidden;
  }

  .categoria-mobile-btn span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .relative-time-badge {
    height: calc(var(--lumi-input-height) + 2px);
    font-size: 0.85rem;
  }

  .paciente-dropdown.show {
    max-height: 250px;
  }

  .paciente-item {
    padding: 0.75rem 0.75rem;
  }
}
</style>
