<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class CategoriaServico extends Model
{
    use HasFactory, LogsActivity;

    protected $table = 'categorias_servicos';

    protected $fillable = [
        'clinica_id',
        'nome',
        'descricao',
        'cor',
        'ativo',
    ];

    protected $casts = [
        'ativo' => 'boolean',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*']);
    }

    // ==================== RELACIONAMENTOS ====================

    public function clinica(): BelongsTo
    {
        return $this->belongsTo(Clinica::class, 'clinica_id');
    }

    public function servicosProdutos(): HasMany
    {
        return $this->hasMany(ServicoProduto::class, 'categoria_id');
    }

    // ==================== SCOPES ====================

    public function scopeDaClinica(Builder $query, int $clinicaId): Builder
    {
        return $query->where('clinica_id', $clinicaId);
    }

    public function scopeAtivas(Builder $query): Builder
    {
        return $query->where('ativo', true);
    }

    // ==================== MÉTODOS ====================

    public function ativar(): void
    {
        $this->update(['ativo' => true]);
    }

    public function desativar(): void
    {
        $this->update(['ativo' => false]);
    }

    public function getQuantidadeServicos(): int
    {
        return $this->servicosProdutos()->count();
    }

    public function getQuantidadeServicosAtivos(): int
    {
        return $this->servicosProdutos()->where('ativo', true)->count();
    }
}
