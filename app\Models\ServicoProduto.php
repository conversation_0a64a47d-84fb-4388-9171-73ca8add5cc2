<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class ServicoProduto extends Model
{
    use HasFactory, LogsActivity;

    protected $table = 'servicos_produtos';

    // Constantes de tipo
    const TIPO_SERVICO = 'servico';
    const TIPO_PRODUTO = 'produto';
    const TIPO_PROCEDIMENTO = 'procedimento';

    protected $fillable = [
        'clinica_id',
        'categoria_id',
        'codigo',
        'nome',
        'descricao',
        'tipo',
        'valor_base',
        'valor_minimo',
        'valor_maximo',
        'unidade',
        'tempo_estimado',
        'observacoes',
        'ativo',
    ];

    protected $casts = [
        'valor_base' => 'decimal:2',
        'valor_minimo' => 'decimal:2',
        'valor_maximo' => 'decimal:2',
        'tempo_estimado' => 'integer',
        'ativo' => 'boolean',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*']);
    }

    // ==================== RELACIONAMENTOS ====================

    public function clinica(): BelongsTo
    {
        return $this->belongsTo(Clinica::class, 'clinica_id');
    }

    public function categoria(): BelongsTo
    {
        return $this->belongsTo(CategoriaServico::class, 'categoria_id');
    }

    public function orcamentoItens(): HasMany
    {
        return $this->hasMany(OrcamentoItem::class, 'servico_produto_id');
    }

    public function templateItens(): HasMany
    {
        return $this->hasMany(OrcamentoTemplateItem::class, 'servico_produto_id');
    }

    // ==================== SCOPES ====================

    public function scopeDaClinica(Builder $query, int $clinicaId): Builder
    {
        return $query->where('clinica_id', $clinicaId);
    }

    public function scopeAtivos(Builder $query): Builder
    {
        return $query->where('ativo', true);
    }

    public function scopePorTipo(Builder $query, string $tipo): Builder
    {
        return $query->where('tipo', $tipo);
    }

    public function scopePorCategoria(Builder $query, int $categoriaId): Builder
    {
        return $query->where('categoria_id', $categoriaId);
    }

    public function scopeBuscar(Builder $query, string $termo): Builder
    {
        return $query->where(function ($q) use ($termo) {
            $q->where('nome', 'like', "%{$termo}%")
              ->orWhere('codigo', 'like', "%{$termo}%")
              ->orWhere('descricao', 'like', "%{$termo}%");
        });
    }

    // ==================== MÉTODOS ====================

    public function ativar(): void
    {
        $this->update(['ativo' => true]);
    }

    public function desativar(): void
    {
        $this->update(['ativo' => false]);
    }

    public function isServico(): bool
    {
        return $this->tipo === self::TIPO_SERVICO;
    }

    public function isProduto(): bool
    {
        return $this->tipo === self::TIPO_PRODUTO;
    }

    public function isProcedimento(): bool
    {
        return $this->tipo === self::TIPO_PROCEDIMENTO;
    }

    public function getTempoEstimadoFormatado(): string
    {
        if (!$this->tempo_estimado) {
            return 'Não definido';
        }

        $horas = intval($this->tempo_estimado / 60);
        $minutos = $this->tempo_estimado % 60;

        if ($horas > 0) {
            return $minutos > 0 ? "{$horas}h {$minutos}min" : "{$horas}h";
        }

        return "{$minutos}min";
    }

    public function getValorFormatado(): string
    {
        return 'R$ ' . number_format($this->valor_base, 2, ',', '.');
    }

    public function getFaixaValorFormatada(): string
    {
        if ($this->valor_minimo && $this->valor_maximo) {
            return 'R$ ' . number_format($this->valor_minimo, 2, ',', '.') . 
                   ' - R$ ' . number_format($this->valor_maximo, 2, ',', '.');
        }

        return $this->getValorFormatado();
    }

    public function validarValor(float $valor): bool
    {
        if ($this->valor_minimo && $valor < $this->valor_minimo) {
            return false;
        }

        if ($this->valor_maximo && $valor > $this->valor_maximo) {
            return false;
        }

        return true;
    }

    public function getQuantidadeUsadaOrcamentos(): int
    {
        return $this->orcamentoItens()->count();
    }

    public static function getTipos(): array
    {
        return [
            self::TIPO_SERVICO => 'Serviço',
            self::TIPO_PRODUTO => 'Produto',
            self::TIPO_PROCEDIMENTO => 'Procedimento',
        ];
    }

    public function getTipoFormatado(): string
    {
        return self::getTipos()[$this->tipo] ?? $this->tipo;
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->codigo) {
                $model->codigo = $model->gerarCodigo();
            }
        });
    }

    private function gerarCodigo(): string
    {
        $prefixo = strtoupper(substr($this->tipo, 0, 4));
        $ultimo = static::where('clinica_id', $this->clinica_id)
                       ->where('codigo', 'like', "{$prefixo}%")
                       ->orderBy('codigo', 'desc')
                       ->first();

        if ($ultimo && preg_match('/(\d+)$/', $ultimo->codigo, $matches)) {
            $numero = intval($matches[1]) + 1;
        } else {
            $numero = 1;
        }

        return $prefixo . str_pad($numero, 3, '0', STR_PAD_LEFT);
    }
}
