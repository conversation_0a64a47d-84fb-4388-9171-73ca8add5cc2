<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class OrcamentoItem extends Model
{
    use HasFactory, LogsActivity;

    protected $table = 'orcamento_itens';

    protected $fillable = [
        'orcamento_id',
        'servico_produto_id',
        'nome',
        'descricao',
        'quantidade',
        'valor_unitario',
        'desconto_percentual',
        'desconto_valor',
        'valor_total',
        'observacoes',
        'ordem',
    ];

    protected $casts = [
        'quantidade' => 'decimal:2',
        'valor_unitario' => 'decimal:2',
        'desconto_percentual' => 'decimal:2',
        'desconto_valor' => 'decimal:2',
        'valor_total' => 'decimal:2',
        'ordem' => 'integer',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*']);
    }

    // ==================== RELACIONAMENTOS ====================

    public function orcamento(): BelongsTo
    {
        return $this->belongsTo(Orcamento::class, 'orcamento_id');
    }

    public function servicoProduto(): BelongsTo
    {
        return $this->belongsTo(ServicoProduto::class, 'servico_produto_id');
    }

    // ==================== MÉTODOS ====================

    public function calcularValorTotal(): void
    {
        $valorBruto = $this->quantidade * $this->valor_unitario;
        $valorDesconto = $this->desconto_valor;

        // Calcular desconto por percentual se não houver valor fixo
        if ($valorDesconto == 0 && $this->desconto_percentual > 0) {
            $valorDesconto = ($valorBruto * $this->desconto_percentual) / 100;
        }

        $this->valor_total = $valorBruto - $valorDesconto;
    }

    public function getValorBruto(): float
    {
        return $this->quantidade * $this->valor_unitario;
    }

    public function getValorDesconto(): float
    {
        $valorBruto = $this->getValorBruto();
        $valorDesconto = $this->desconto_valor;

        if ($valorDesconto == 0 && $this->desconto_percentual > 0) {
            $valorDesconto = ($valorBruto * $this->desconto_percentual) / 100;
        }

        return $valorDesconto;
    }

    public function getValorUnitarioFormatado(): string
    {
        return 'R$ ' . number_format($this->valor_unitario, 2, ',', '.');
    }

    public function getValorTotalFormatado(): string
    {
        return 'R$ ' . number_format($this->valor_total, 2, ',', '.');
    }

    public function getQuantidadeFormatada(): string
    {
        if ($this->quantidade == intval($this->quantidade)) {
            return number_format($this->quantidade, 0, ',', '.');
        }
        
        return number_format($this->quantidade, 2, ',', '.');
    }

    public function temDesconto(): bool
    {
        return $this->desconto_valor > 0 || $this->desconto_percentual > 0;
    }

    public function getDescontoFormatado(): string
    {
        if ($this->desconto_valor > 0) {
            return 'R$ ' . number_format($this->desconto_valor, 2, ',', '.');
        }

        if ($this->desconto_percentual > 0) {
            return number_format($this->desconto_percentual, 2, ',', '.') . '%';
        }

        return 'Sem desconto';
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            $model->calcularValorTotal();
        });

        static::saved(function ($model) {
            // Recalcular valores do orçamento
            if ($model->orcamento) {
                $model->orcamento->calcularValores();
            }
        });

        static::deleted(function ($model) {
            // Recalcular valores do orçamento
            if ($model->orcamento) {
                $model->orcamento->calcularValores();
            }
        });
    }
}
