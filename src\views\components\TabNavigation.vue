<template>
    <div class="tab-navigation-container d-flex align-items-center">
        <v-tabs center-active fixed-tabs v-model="currentTab" class="flex-grow-1" style="color: #FFF;">
            <!-- <v-tab @click="openPage('inicio')">
                <div class="pb-1">
                    <v-icon>mdi-home</v-icon>
                </div>
            </v-tab> -->
            <v-tab @click="openPage('agenda')">
                <div class="pb-1">
                    <v-icon class="tab-icon">mdi-calendar-month</v-icon>
                </div>
                <!-- <i class="fas fa-calendar-alt mr-3 pt-1 d-none d-md-block"></i> -->
                <span class="tab-text">{{ $t('mainNav.agenda') }}</span>
            </v-tab>
            <v-tab @click="openPage('pacientes')" value="pacientes">
                <div class="pb-1">
                    <v-icon class="tab-icon">mdi-account-details</v-icon>
                </div>
                <span class="tab-text">{{ $t('mainNav.patients') }}</span>
            </v-tab>

            <v-tab @click="openPage('mentorias')">
                <div class="pb-1">
                    <v-icon class="tab-icon">mdi-school</v-icon>
                </div>
                <span class="tab-text">{{ $t('mainNav.mentoring') }}</span>
            </v-tab>

            <v-tab @click="openPage('financeiro')">
                <div class="pb-1">
                    <v-icon class="tab-icon">mdi-currency-usd</v-icon>
                </div>
                <span class="tab-text">{{ $t('mainNav.financial') }}</span>
            </v-tab>

            <v-tab @click="openPage('ortodontistas')" v-if="$user.system_admin" style="max-width: 80px;">
                <div class="pb-1">
                    <v-icon class="tab-icon">mdi-doctor</v-icon>
                </div>
                <span class="tab-text d-none"><!-- Sem texto para este tab --></span>
            </v-tab>


        </v-tabs>

        <!-- Componente de notificações -->
        <div class="notification-container ms-3">
            <notification-dropdown />
        </div>
    </div>
</template>

<script>

var tab = 0
import { useRoute } from 'vue-router';
import NotificationDropdown from '@/components/NotificationDropdown.vue';

export default {
    name: "tab-navigation",
    components: {
        NotificationDropdown
    },
    data() {
        return {
            tab,
            currentTab: 0
        }
    },
    methods: {
        openPage(page) {
            this.$router.push(`/${page}`)
        },

        updateCurrentTab() {
            let currentPathObject = this.$router.currentRoute.value.path.replace('/', '').split('/')[0]

            var tabsMap = {
                '': 0,
                'agenda': 0,
                'paciente': 1,
                'pacientes': 1,
                'mentorias': 2,
                'financeiro': 3,
                'ortodontista': 4,
                'ortodontistas': 4,
            }

            this.currentTab = tabsMap[currentPathObject] || 0;
        }
    },
    mounted() {
        // Definir aba inicial baseada na rota atual
        this.updateCurrentTab();
    },
    watch: {
        // Observar mudanças na rota para atualizar a aba ativa
        '$route'() {
            this.updateCurrentTab();
        }
    },
    computed: {
        route: () => useRoute()
    }
}
</script>

<style scoped>
.tab-navigation-container {
    background-color: var(--v-theme-deep-purple-darken-4);
}

.notification-container {
    /* padding-right: 16px; */
}

/* Garantir que o dropdown apareça acima de outros elementos */
.notification-container :deep(.notification-dropdown) {
    z-index: 1060;
}

/* Regras responsivas para ícones e texto dos tabs */

/* Mobile (< 576px): Mostrar apenas ícones, esconder texto */
@media (max-width: 575.98px) {
    .tab-icon {
        display: inline-block !important;
        margin-right: 0 !important;
    }

    .tab-text {
        display: none !important;
    }
}

/* Tablet pequeno (576px - 767px): Esconder ícones, mostrar texto (regra atual) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .tab-icon {
        display: none !important;
    }

    .tab-text {
        display: inline-block !important;
    }
}

/* Desktop (≥ 768px): Mostrar ícones e texto */
@media (min-width: 768px) {
    .tab-icon {
        display: inline-block !important;
        margin-right: 0.75rem !important; /* equivalente ao mr-3 */
    }

    .tab-text {
        display: inline-block !important;
    }
}
</style>