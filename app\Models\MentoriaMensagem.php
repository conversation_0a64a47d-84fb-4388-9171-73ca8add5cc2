<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class MentoriaMensagem extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'mentoria_mensagens';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'mentoria_id',
        'remetente_id',
        'mensagem',
        'tipo',
        'lida',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'lida' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*']);
    }

    // Relacionamentos
    public function mentoria()
    {
        return $this->belongsTo(Mentoria::class, 'mentoria_id');
    }

    public function remetente()
    {
        return $this->belongsTo(Dentista::class, 'remetente_id');
    }
}
